import React from "react"
import { graphql } from "gatsby"
import Layout from "../components/layout"
import Seo from "../components/seo"
import styled from "styled-components"

const StyledDiv = styled.div`
  & h1 {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.5;
    margin-bottom: 1rem;
    color: #111827;
  }
  & h2 {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 0.75rem;
    margin-top: 2rem;
    color: #111827;
  }
  & h3 {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    margin-top: 1.5rem;
    color: #111827;
  }
  & h4 {
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    margin-top: 1rem;
    color: #111827;
  }
  & p {
    margin-bottom: 1rem;
    line-height: 1.7;
    color: #374151;
  }
  & a {
    color: #7c3aed;
    text-decoration: underline;
    &:hover {
      color: #5b21b6;
    }
  }
  & strong {
    font-weight: 600;
    color: #111827;
  }
  & ul, & ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }
  & li {
    margin-bottom: 0.25rem;
    line-height: 1.7;
    color: #374151;
  }
  & blockquote {
    border-left: 4px solid #7c3aed;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6b7280;
  }
`

const CoursePostTemplate = ({ data }) => {
  const { markdownRemark: post } = data

  return (
    <Layout>
      <Seo
        title={post.frontmatter.title}
        description={post.frontmatter.description}
      />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
              {post.frontmatter.courseType}
            </span>
            <span className="ml-2 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
              {post.frontmatter.level}
            </span>
            {post.frontmatter.available ? (
              <span className="ml-2 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                Available
              </span>
            ) : (
              <span className="ml-2 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                Full
              </span>
            )}
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {post.frontmatter.title}
          </h1>
          
          <p className="text-xl text-gray-600 mb-6">
            {post.frontmatter.description}
          </p>
          
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
            <span>Instructor: {post.frontmatter.instructor}</span>
            <span>•</span>
            <span>{post.frontmatter.duration}</span>
          </div>
        </div>

        {/* Course Image */}
        {post.frontmatter.courseImage && (
          <div className="mb-12">
            <img
              src={post.frontmatter.courseImage?.publicURL}
              alt={post.frontmatter.title}
              className="w-full h-96 object-cover rounded-lg shadow-lg"
            />
          </div>
        )}

        {/* Course Info Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-12">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {post.frontmatter.price}
            </div>
            <div className="text-sm text-gray-600">Course Fee</div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {post.frontmatter.duration}
            </div>
            <div className="text-sm text-gray-600">Duration</div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {post.frontmatter.level}
            </div>
            <div className="text-sm text-gray-600">Level</div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {post.frontmatter.courseType}
            </div>
            <div className="text-sm text-gray-600">Focus Area</div>
          </div>
        </div>

        {/* Course Details */}
        <StyledDiv
          className="max-w-none mb-12"
          dangerouslySetInnerHTML={{ __html: post.html }}
        />

        {/* Instructor Info */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-8 mb-12">
          <h3 className="text-xl font-bold text-gray-900 mb-4">About Your Instructor</h3>
          <p className="text-lg text-gray-900 font-semibold mb-2">
            {post.frontmatter.instructor}
          </p>
          <p className="text-gray-700">
            Learn from experienced professionals who understand the unique challenges and opportunities 
            facing AAPI students in theatre arts.
          </p>
        </div>

        {/* Registration CTA */}
        <div className="bg-white border-2 border-purple-200 rounded-lg p-8 text-center mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Register?
          </h3>
          <p className="text-lg text-gray-600 mb-6">
            {post.frontmatter.available 
              ? "Spaces are available for this course. Register now to secure your spot!"
              : "This course is currently full, but you can join our waitlist for future sessions."
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/booking"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-pink to-purple hover:from-pink-600 hover:to-purple-600"
            >
              {post.frontmatter.available ? "Register Now" : "Join Waitlist"}
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Ask Questions
            </a>
          </div>
        </div>

        {/* Course Features */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            What Makes Our Courses Special
          </h3>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Cultural Understanding</h4>
              <p className="text-gray-600">
                Instructors who understand AAPI experiences and cultural nuances.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-pink-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Small Class Sizes</h4>
              <p className="text-gray-600">
                Personalized attention and meaningful connections with fellow students.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Industry Preparation</h4>
              <p className="text-gray-600">
                Practical skills and strategies for professional success.
              </p>
            </div>
          </div>
        </div>

        {/* Related Courses */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Explore More Courses
          </h3>
          <div className="text-center">
            <a
              href="/booking"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-pink to-purple hover:from-pink-600 hover:to-purple-600"
            >
              View All Courses
            </a>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default CoursePostTemplate

export const pageQuery = graphql`
  query CoursePostByID($id: String!) {
    markdownRemark(id: { eq: $id }) {
      id
      html
      frontmatter {
        title
        instructor
        level
        duration
        price
        courseImage {
          publicURL
        }
        courseType
        description
        available
      }
    }
  }
`
