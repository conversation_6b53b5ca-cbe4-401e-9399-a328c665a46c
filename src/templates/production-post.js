import React from "react"
import { graphql } from "gatsby"
import Layout from "../components/layout"
import Seo from "../components/seo"
import styled from "styled-components"

const StyledDiv = styled.div`
  & h1 {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.5;
    margin-bottom: 1rem;
    color: #111827;
  }
  & h2 {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 0.75rem;
    margin-top: 2rem;
    color: #111827;
  }
  & h3 {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    margin-top: 1.5rem;
    color: #111827;
  }
  & h4 {
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    margin-top: 1rem;
    color: #111827;
  }
  & p {
    margin-bottom: 1rem;
    line-height: 1.7;
    color: #374151;
  }
  & a {
    color: #7c3aed;
    text-decoration: underline;
    &:hover {
      color: #5b21b6;
    }
  }
  & strong {
    font-weight: 600;
    color: #111827;
  }
  & ul, & ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }
  & li {
    margin-bottom: 0.25rem;
    line-height: 1.7;
    color: #374151;
  }
  & blockquote {
    border-left: 4px solid #7c3aed;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6b7280;
  }
`

const ProductionPostTemplate = ({ data }) => {
  const { markdownRemark: post } = data

  return (
    <Layout>
      <Seo
        title={post.frontmatter.title}
        description={post.frontmatter.synopsis}
      />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
              {post.frontmatter.genre}
            </span>
            {post.frontmatter.featured && (
              <span className="ml-2 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-pink-100 text-pink-800">
                Featured Production
              </span>
            )}
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {post.frontmatter.title}
          </h1>
          
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-500 mb-6">
            <span>Directed by {post.frontmatter.director}</span>
            <span>•</span>
            <span>{post.frontmatter.venue}</span>
            <span>•</span>
            <span>{new Date(post.frontmatter.date).toLocaleDateString()}</span>
          </div>
        </div>

        {/* Production Photo */}
        {post.frontmatter.productionPhoto && (
          <div className="mb-12">
            <img
              src={post.frontmatter.productionPhoto?.publicURL}
              alt={post.frontmatter.title}
              className="w-full h-96 object-cover rounded-lg shadow-lg"
            />
          </div>
        )}

        {/* Synopsis */}
        <div className="mb-12 bg-gray-50 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Synopsis</h2>
          <p className="text-lg text-gray-700 leading-relaxed">
            {post.frontmatter.synopsis}
          </p>
        </div>

        {/* Cast & Crew */}
        {post.frontmatter.castCrew && post.frontmatter.castCrew.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Cast & Crew</h2>
            <div className="grid gap-2 md:grid-cols-2">
              {post.frontmatter.castCrew.map((member, index) => (
                <div key={index} className="bg-white p-4 rounded-lg shadow">
                  <p className="text-gray-800">{member}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Production Details */}
        <StyledDiv
          className="max-w-none mb-12"
          dangerouslySetInnerHTML={{ __html: post.html }}
        />

        {/* Production Info Box */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-8 mb-12">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Production Information</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <p className="text-sm font-medium text-gray-600">Director</p>
              <p className="text-lg text-gray-900">{post.frontmatter.director}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Venue</p>
              <p className="text-lg text-gray-900">{post.frontmatter.venue}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Genre</p>
              <p className="text-lg text-gray-900">{post.frontmatter.genre}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Performance Date</p>
              <p className="text-lg text-gray-900">
                {new Date(post.frontmatter.date).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        {/* Share Section */}
        <div className="mt-16 pt-8 border-t border-gray-200">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Share This Production
            </h3>
            <div className="flex justify-center space-x-4">
              <a
                href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(post.frontmatter.title)}&url=${encodeURIComponent(typeof window !== 'undefined' ? window.location.href : '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Share on Twitter
              </a>
              <a
                href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(typeof window !== 'undefined' ? window.location.href : '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Share on Facebook
              </a>
            </div>
          </div>
        </div>

        {/* Related Productions */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            More Productions
          </h3>
          <div className="text-center">
            <a
              href="/productions"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-pink to-purple hover:from-pink-600 hover:to-purple-600"
            >
              View All Productions
            </a>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default ProductionPostTemplate

export const pageQuery = graphql`
  query ProductionPostByID($id: String!) {
    markdownRemark(id: { eq: $id }) {
      id
      html
      frontmatter {
        title
        director
        date
        productionPhoto {
          publicURL
        }
        venue
        genre
        castCrew
        synopsis
        featured
      }
    }
  }
`
