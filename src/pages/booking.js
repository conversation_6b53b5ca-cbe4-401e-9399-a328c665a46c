import * as React from "react"
import { graphql } from "gatsby"
import Layout from "../components/layout"
import Seo from "../components/seo"

const BookingPage = ({ data }) => {
  const courses = data.allMarkdownRemark.edges

  return (
    <div className="h-auto w-screen">
      <Layout>
        <Seo
          title="Book Drama Classes - ASATA"
          description="Enroll in specialized drama courses designed for AAPI students. From acting fundamentals to advanced workshops, find the perfect class for your theatre journey."
        />
        
        {/* Header Section */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
                <span className="text-gradient bg-gradient-to-r from-pink to-purple">
                  Drama Classes
                </span>
              </h1>
              <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
                Develop your craft with courses designed specifically for AAPI students. 
                Learn from experienced instructors who understand your unique journey in theatre arts.
              </p>
            </div>
          </div>
        </div>

        {/* Quick Booking Form */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="bg-white rounded-lg shadow-lg p-8 mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Quick Class Booking
            </h2>
            <div className="w-full">
              <iframe 
                title="Booking Form"
                src="https://docs.google.com/forms/d/e/1FAIpQLSfyvZxGFoq1_SAY9MFLwDHgLxdL6YP_Q56MQX2BOZwl_9QY8w/viewform?embedded=true" 
                width="100%" 
                height="800" 
                frameBorder="0" 
                marginHeight="0" 
                marginWidth="0"
                className="rounded-lg"
              >
                Loading...
              </iframe>
            </div>
          </div>
        </div>

        {/* Available Courses */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Available Courses
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Choose from our range of specialized courses designed to support AAPI students 
                in developing their theatre skills while honoring their cultural identity.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {courses.map(({ node }) => (
                <div key={node.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  {node.frontmatter.courseImage && (
                    <div className="h-48 bg-gray-200">
                      <img
                        src={node.frontmatter.courseImage?.publicURL}
                        alt={node.frontmatter.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        {node.frontmatter.courseType}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {node.frontmatter.level}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {node.frontmatter.title}
                    </h3>
                    
                    <p className="text-sm text-gray-600 mb-2">
                      <span className="font-medium">Instructor:</span> {node.frontmatter.instructor}
                    </p>
                    
                    <p className="text-sm text-gray-600 mb-2">
                      <span className="font-medium">Duration:</span> {node.frontmatter.duration}
                    </p>
                    
                    <p className="text-lg font-semibold text-purple-600 mb-3">
                      {node.frontmatter.price}
                    </p>
                    
                    <p className="text-sm text-gray-700 mb-4 line-clamp-3">
                      {node.frontmatter.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      {node.frontmatter.available ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Available
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Full
                        </span>
                      )}
                      
                      <a
                        href={node.fields.slug}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
                      >
                        Learn More
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Course Benefits */}
        <div className="bg-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose ASATA Classes?
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Our courses are specifically designed to address the unique experiences and 
                challenges faced by AAPI students in theatre arts.
              </p>
            </div>
            
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
              <div className="text-center">
                <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Cultural Understanding</h3>
                <p className="text-gray-600">
                  Instructors who understand AAPI experiences and cultural nuances.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-pink-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentic Representation</h3>
                <p className="text-gray-600">
                  Learn to portray characters authentically without falling into stereotypes.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Community Building</h3>
                <p className="text-gray-600">
                  Connect with fellow AAPI students and build lasting professional relationships.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Industry Preparation</h3>
                <p className="text-gray-600">
                  Practical skills and strategies for navigating the professional theatre world.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Questions About Our Classes?
              </h2>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                Our team is here to help you find the perfect class for your goals and experience level. 
                Don't hesitate to reach out with any questions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-pink to-purple hover:from-pink-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
                >
                  Contact Us
                </a>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
                >
                  Email Us
                </a>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </div>
  )
}

export default BookingPage

export const query = graphql`
  query {
    allMarkdownRemark(
      filter: { frontmatter: { templateKey: { eq: "course-post" } } }
      sort: { frontmatter: { title: ASC } }
    ) {
      edges {
        node {
          id
          fields {
            slug
          }
          frontmatter {
            title
            instructor
            level
            duration
            price
            courseImage {
              publicURL
            }
            courseType
            description
            available
          }
        }
      }
    }
  }
`
