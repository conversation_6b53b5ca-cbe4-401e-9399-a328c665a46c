import * as React from "react"
import { graphql } from "gatsby"
import Layout from "../components/layout"
import Seo from "../components/seo"

const ProductionsPage = ({ data }) => {
  const productions = data.allMarkdownRemark.edges

  return (
    <div className="h-auto w-screen">
      <Layout>
        <Seo
          title="Theatre Productions - ASATA"
          description="Explore outstanding theatre productions featuring AAPI students and stories. Discover the creative work that's shaping the future of inclusive theatre."
        />
        
        {/* Header Section */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
                <span className="text-gradient bg-gradient-to-r from-pink to-purple">
                  Theatre Productions
                </span>
              </h1>
              <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
                Celebrating the creative work of AAPI students and artists. From classical revivals to 
                original devised pieces, these productions showcase the diversity and talent of our community.
              </p>
            </div>
          </div>
        </div>

        {/* Productions Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {productions.map(({ node }) => (
              <div key={node.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                {node.frontmatter.productionPhoto && (
                  <div className="h-48 bg-gray-200">
                    <img
                      src={node.frontmatter.productionPhoto?.publicURL}
                      alt={node.frontmatter.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                
                <div className="p-6">
                  <div className="flex items-center mb-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      {node.frontmatter.genre}
                    </span>
                    {node.frontmatter.featured && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                        Featured
                      </span>
                    )}
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {node.frontmatter.title}
                  </h3>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Director:</span> {node.frontmatter.director}
                  </p>
                  
                  <p className="text-sm text-gray-600 mb-3">
                    <span className="font-medium">Venue:</span> {node.frontmatter.venue}
                  </p>
                  
                  <p className="text-sm text-gray-700 mb-4 line-clamp-3">
                    {node.frontmatter.synopsis}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {new Date(node.frontmatter.date).toLocaleDateString()}
                    </span>
                    
                    <a
                      href={node.fields.slug}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
                    >
                      Learn More
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Featured Section */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Production Highlights
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                These productions represent the innovative and impactful work being created by 
                AAPI students and artists across the country.
              </p>
            </div>
            
            <div className="grid gap-8 md:grid-cols-3">
              <div className="text-center">
                <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Original Works</h3>
                <p className="text-gray-600">
                  Student-created productions that explore AAPI experiences through innovative storytelling.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-pink-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Classical Revivals</h3>
                <p className="text-gray-600">
                  Fresh interpretations of classic works through contemporary AAPI perspectives.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Collaborative Projects</h3>
                <p className="text-gray-600">
                  Community-driven productions that bring together artists from diverse backgrounds.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Submit Your Production
              </h2>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                Have you been part of a production that showcases AAPI talent and stories? 
                We'd love to feature your work and celebrate your achievements.
              </p>
              <a
                href="/contact"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-pink to-purple hover:from-pink-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
              >
                Share Your Production
              </a>
            </div>
          </div>
        </div>
      </Layout>
    </div>
  )
}

export default ProductionsPage

export const query = graphql`
  query {
    allMarkdownRemark(
      filter: { frontmatter: { templateKey: { eq: "production-post" } } }
      sort: { frontmatter: { date: DESC } }
    ) {
      edges {
        node {
          id
          fields {
            slug
          }
          frontmatter {
            title
            director
            date
            productionPhoto {
              publicURL
            }
            venue
            genre
            synopsis
            featured
          }
        }
      }
    }
  }
`
