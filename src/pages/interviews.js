import * as React from "react"
import { graphql } from "gatsby"
import Layout from "../components/layout"
import Seo from "../components/seo"

const InterviewsPage = ({ data }) => {
  const interviews = data.allMarkdownRemark.edges

  return (
    <div className="h-auto w-screen">
      <Layout>
        <Seo
          title="Student Interviews - ASATA"
          description="Discover inspiring stories from AAPI students making their mark in theatre arts. Read interviews about their journeys, challenges, and successes."
        />
        
        {/* Header Section */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
                <span className="text-gradient bg-gradient-to-r from-pink to-purple">
                  Student Interviews
                </span>
              </h1>
              <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
                Hear from AAPI students who are breaking barriers and creating change in theatre arts. 
                Their stories inspire and illuminate the path forward for the next generation.
              </p>
            </div>
          </div>
        </div>

        {/* Interviews Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {interviews.map(({ node }) => (
              <div key={node.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                {node.frontmatter.studentPhoto && (
                  <div className="h-48 bg-gray-200">
                    <img
                      src={node.frontmatter.studentPhoto?.publicURL}
                      alt={node.frontmatter.studentName}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                
                <div className="p-6">
                  <div className="flex items-center mb-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      {node.frontmatter.specialization}
                    </span>
                    {node.frontmatter.featured && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                        Featured
                      </span>
                    )}
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {node.frontmatter.studentName}
                  </h3>
                  
                  <h4 className="text-lg text-gray-700 mb-3">
                    {node.frontmatter.title}
                  </h4>
                  
                  <p className="text-sm text-gray-600 mb-2">
                    {node.frontmatter.school}
                  </p>
                  
                  <p className="text-sm text-gray-500 mb-4">
                    {node.frontmatter.experience}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {new Date(node.frontmatter.date).toLocaleDateString()}
                    </span>
                    
                    <a
                      href={node.fields.slug}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
                    >
                      Read Interview
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Share Your Story
              </h2>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                Are you an AAPI student with a theatre story to tell? We'd love to feature your journey 
                and inspire others in our community.
              </p>
              <a
                href="/contact"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-pink to-purple hover:from-pink-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
              >
                Get in Touch
              </a>
            </div>
          </div>
        </div>
      </Layout>
    </div>
  )
}

export default InterviewsPage

export const query = graphql`
  query {
    allMarkdownRemark(
      filter: { frontmatter: { templateKey: { eq: "interview-post" } } }
      sort: { frontmatter: { date: DESC } }
    ) {
      edges {
        node {
          id
          fields {
            slug
          }
          frontmatter {
            studentName
            title
            date
            studentPhoto {
              publicURL
            }
            school
            experience
            specialization
            featured
          }
        }
      }
    }
  }
`
