# ASATA Website Configuration Changes

This document outlines all the changes made to transform the Holo theme into the AAPI Student Alliance in Theatre Arts (ASATA) website.

## Overview

The website has been reconfigured to serve the needs of AAPI students in theatre arts, featuring:
- Student interview showcases
- Theatre production highlights
- Drama course booking system
- Integrated Google Forms for contact and registration

## Configuration Changes

### 1. Site Metadata (gatsby-config.js)
- **Title**: Changed from "Holo - Gatsby Starter" to "AAPI Student Alliance in Theatre Arts (ASATA)"
- **Description**: Updated to reflect ASATA's mission
- **Site URL**: Changed to "https://asata-theatre.netlify.app/"
- **Author**: Updated to "ASATA Team"
- **Manifest**: Updated app name and short name

### 2. Navigation (src/components/navbar.js)
- **Brand**: Changed from "HOLO" to "ASATA"
- **Menu Items**: 
  - Removed: Gallery, Blog
  - Added: Interviews, Productions, Book Classes
  - Kept: Home, About
  - Updated: Call-to-action button changed from "Call Us Now" to "Contact Us"

### 3. CMS Configuration (static/admin/config.yml)
Added three new content types:

#### Student Interviews
- Student name, photo, school, experience
- Specialization (Acting, Directing, etc.)
- Interview content and featured status

#### Theatre Productions
- Production title, director, venue, genre
- Cast & crew list, synopsis
- Production photos and featured status

#### Drama Courses
- Course title, instructor, level, duration, price
- Course type, description, availability
- Course images and detailed information

## New Pages Created

### 1. Interviews Page (src/pages/interviews.js)
- Displays all student interviews in a grid layout
- Filtering by specialization and featured status
- Links to individual interview pages
- Call-to-action for story submissions

### 2. Productions Page (src/pages/productions.js)
- Showcases theatre productions featuring AAPI students
- Genre-based categorization
- Production highlights and features
- Submission form for new productions

### 3. Booking Page (src/pages/booking.js)
- **Integrated Google Form**: Your provided iframe is embedded for course registration
- Course catalog with filtering options
- Course benefits and features
- Direct links to course detail pages

## Template Files Created

### 1. Interview Template (src/templates/interview-post.js)
- Individual interview page layout
- Student photo and information display
- Social sharing functionality
- Related interviews section

### 2. Production Template (src/templates/production-post.js)
- Production detail pages
- Cast & crew information
- Synopsis and production photos
- Performance information box

### 3. Course Template (src/templates/course-post.js)
- Detailed course information
- Instructor profiles
- Registration call-to-action
- Course features and benefits

## Content Updates

### 1. Demo Content Created

#### Student Interviews
- **Sarah Chen**: UCLA student specializing in Acting
- **Marcus Kim**: Yale student specializing in Stage Design

#### Theatre Productions
- **Golden Child Revival**: Berkeley Repertory Theatre production
- **Letters to Home**: Student devised work at UCLA

#### Drama Courses
- **Acting Fundamentals for AAPI Students**: 8-week beginner course
- **Voice and Speech for AAPI Performers**: 6-week specialized workshop

### 2. Blog Content
- Updated existing blog post to focus on AAPI representation in theatre
- Changed from Lorem Ipsum to relevant content about diversity and inclusion

## Page Updates

### 1. Home Page (src/pages/index.js)
- Updated SEO title and description
- Modified header component for ASATA branding

### 2. Header Component (src/components/Home/header.js)
- **Welcome Message**: Changed to "WELCOME TO ASATA"
- **Tagline**: "Empowering AAPI Voices"
- **Description**: Updated to reflect ASATA's mission
- **Call-to-Action Buttons**: 
  - Primary: "Book Classes" (links to /booking)
  - Secondary: "Student Stories" (links to /interviews)

### 3. Contact Page (src/pages/contact.js)
- **Google Form Integration**: Your provided iframe is embedded
- Updated contact information:
  - Phone: (213) 555-ASATA
  - Email: <EMAIL>
  - Location: Los Angeles, CA & Online
  - Hours: Mon-Fri 9AM-6PM PST
- Updated branding and messaging

### 4. About Page Components
- **aboutHeader.js**: Updated title from "What is Holo?" to "What is ASATA?" and mission statement
- **aboutPage.js**: Changed statistics from technical metrics to ASATA community metrics (interviews, productions, courses, members)
- **aboutGrid.js**: Completely redesigned to showcase ASATA's core values (Representation, Education, Community)
- **aboutExtra.js**: Updated call-to-action section with community joining message and "JOIN ASATA" button
- **about.js**: Updated SEO title and description to reflect ASATA's mission

## Technical Updates

### 1. Gatsby Node Configuration (gatsby-node.js)
- Added page generation for new content types:
  - interview-post template
  - production-post template
  - course-post template
- Updated context passing to include post IDs

### 2. GraphQL Queries
- Created queries for each new content type
- Added filtering and sorting capabilities
- Implemented featured content highlighting

## Google Forms Integration

Your Google Form has been integrated in two locations:

1. **Contact Page**: Full-width iframe for general inquiries
2. **Booking Page**: Embedded for course registration

The iframe code used:
```html
<iframe 
  src="https://docs.google.com/forms/d/e/1FAIpQLSfyvZxGFoq1_SAY9MFLwDHgLxdL6YP_Q56MQX2BOZwl_9QY8w/viewform?embedded=true" 
  width="100%" 
  height="800" 
  frameborder="0" 
  marginheight="0" 
  marginwidth="0"
>
  Loading...
</iframe>
```

## Features Implemented

### 1. Content Management
- Full CMS support for all content types
- Image upload capabilities
- Rich text editing for detailed content

### 2. User Experience
- Responsive design maintained
- Intuitive navigation structure
- Clear call-to-action buttons
- Social sharing functionality

### 3. SEO Optimization
- Updated meta titles and descriptions
- Proper heading structure
- Alt text for images
- Semantic HTML structure

## Next Steps

To complete the website setup:

1. **Add Images**: Upload appropriate images to `/static/img/` folder
2. **Content Review**: Review and edit demo content as needed
3. **Testing**: Test all forms and navigation links
4. **Deployment**: Deploy to your preferred hosting platform
5. **CMS Setup**: Configure Decap CMS for content management

## File Structure

```
src/
├── pages/
│   ├── interviews.js (new)
│   ├── productions.js (new)
│   ├── booking.js (new)
│   ├── index.js (updated)
│   ├── about.js (updated)
│   └── contact.js (updated)
├── templates/
│   ├── interview-post.js (new)
│   ├── production-post.js (new)
│   └── course-post.js (new)
├── interviews/ (new)
│   ├── sarah-chen-interview.md
│   └── marcus-kim-interview.md
├── productions/ (new)
│   ├── golden-child-revival.md
│   └── letters-to-home.md
├── courses/ (new)
│   ├── acting-fundamentals-aapi.md
│   └── voice-and-speech-workshop.md
└── components/
    ├── navbar.js (updated)
    └── Home/
        └── header.js (updated)
```

The website is now fully configured for ASATA's needs with integrated Google Forms for course booking and contact management.
