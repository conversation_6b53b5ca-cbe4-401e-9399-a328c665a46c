{"name": "Holo-Gatsby-Theme", "private": true, "description": "Holo is a visually striking and highly customizable open source theme built on the powerful Gatsby framework and integrated with the versatile Decap CMS.", "version": "1.0.0", "author": "<PERSON> <<EMAIL>>", "dependencies": {"@fontsource/montserrat": "^5.1.0", "@fontsource/noto-serif": "^5.1.0", "axios": "^1.7.7", "decap-cms-app": "^3.3.3", "gatsby": "^5.14.1", "gatsby-plugin-decap-cms": "^4.0.3", "gatsby-plugin-gatsby-cloud": "^5.12.2", "gatsby-plugin-image": "^3.12.3", "gatsby-plugin-manifest": "^5.14.0", "gatsby-plugin-netlify": "^5.1.1", "gatsby-plugin-netlify-cms": "^7.12.1", "gatsby-plugin-offline": "^6.13.3", "gatsby-plugin-sharp": "^5.14.0", "gatsby-plugin-sitemap": "^6.14.0", "gatsby-remark-copy-linked-files": "^6.13.2", "gatsby-remark-images": "^7.13.2", "gatsby-remark-prismjs": "^7.14.0", "gatsby-remark-relative-images": "^2.0.5", "gatsby-remark-responsive-iframe": "^6.13.2", "gatsby-remark-smartypants": "^6.13.0", "gatsby-source-filesystem": "^5.12.1", "gatsby-transformer-remark": "^6.13.0", "gatsby-transformer-sharp": "^5.14.0", "i": "^0.3.7", "netlify-cms-app": "^2.15.72", "npm": "^10.9.0", "prop-types": "^15.7.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-reveal": "^1.2.2", "remark-mdx": "^3.1.0", "styled-components": "^6.1.13"}, "devDependencies": {"autoprefixer": "^10.4.20", "gatsby-plugin-postcss": "^6.14.0", "postcss": "^8.4.47", "prettier": "3.3.3", "tailwindcss": "^3.4.14"}, "keywords": ["gatsby"], "license": "MIT License", "scripts": {"build": "gatsby build", "develop": "gatsby develop", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "start": "npm run develop", "serve": "gatsby serve", "clean": "gatsby clean", "test": "echo \"Write tests! -> https://gatsby.dev/unit-testing\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/gatsbyjs/gatsby-starter-default"}, "bugs": {"url": "https://github.com/gatsbyjs/gatsby/issues"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}