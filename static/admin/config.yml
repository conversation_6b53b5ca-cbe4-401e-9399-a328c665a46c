backend:
  name: git-gateway
  branch: main
  commit_messages:
    create: "Create {{collection}} “{{slug}}”"
    update: "Update {{collection}} “{{slug}}”"
    delete: "Delete {{collection}} “{{slug}}”"
    uploadMedia: "[skip ci] Upload “{{path}}”"
    deleteMedia: "[skip ci] Delete “{{path}}”"

local_backend: true
media_folder: static/img
public_folder: /img
publish_mode: editorial_workflow # For Drafts

collections:
  - name: "blogs"
    label: "Blogs"
    folder: "src/blog"
    create: true
    slug: "{{slug}}"
    fields:
      - {
          label: "Template Key",
          name: "template<PERSON><PERSON>",
          widget: "hidden",
          default: "blog-post",
        }
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Publish Date", name: "date", widget: "datetime" }
      - {
          label: "Featured Image",
          name: "featuredimage",
          widget: image,
          choose_url: false
        }
      - { label: "Categories", name: "categories", widget: "list" }
      - { label: "Tags", name: "tags", widget: "list" }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Body", name: "body", widget: "markdown" }

  - name: "interviews"
    label: "Student Interviews"
    folder: "src/blog"
    filter: {field: "templateKey", value: "interview-post"}
    create: true
    slug: "{{slug}}"
    fields:
      - {
          label: "Template Key",
          name: "templateKey",
          widget: "hidden",
          default: "interview-post",
        }
      - { label: "Student Name", name: "studentName", widget: "string" }
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Interview Date", name: "date", widget: "datetime" }
      - {
          label: "Student Photo",
          name: "studentPhoto",
          widget: image,
          choose_url: false
        }
      - { label: "School/Institution", name: "school", widget: "string" }
      - { label: "Theatre Experience", name: "experience", widget: "text" }
      - { label: "Specialization", name: "specialization", widget: "select", options: ["Acting", "Directing", "Playwriting", "Stage Design", "Lighting", "Sound", "Costume Design", "Other"] }
      - { label: "Interview Content", name: "body", widget: "markdown" }
      - { label: "Featured", name: "featured", widget: "boolean", default: false }

  - name: "productions"
    label: "Theatre Productions"
    folder: "src/blog"
    filter: {field: "templateKey", value: "production-post"}
    create: true
    slug: "{{slug}}"
    fields:
      - {
          label: "Template Key",
          name: "templateKey",
          widget: "hidden",
          default: "production-post",
        }
      - { label: "Production Title", name: "title", widget: "string" }
      - { label: "Director", name: "director", widget: "string" }
      - { label: "Performance Date", name: "date", widget: "datetime" }
      - {
          label: "Production Photo",
          name: "productionPhoto",
          widget: image,
          choose_url: false
        }
      - { label: "Theatre/Venue", name: "venue", widget: "string" }
      - { label: "Genre", name: "genre", widget: "select", options: ["Drama", "Comedy", "Musical", "Experimental", "Classical", "Contemporary", "Other"] }
      - { label: "Cast & Crew", name: "castCrew", widget: "list" }
      - { label: "Synopsis", name: "synopsis", widget: "text" }
      - { label: "Production Details", name: "body", widget: "markdown" }
      - { label: "Featured", name: "featured", widget: "boolean", default: false }

  - name: "courses"
    label: "Drama Courses"
    folder: "src/blog"
    filter: {field: "templateKey", value: "course-post"}
    create: true
    slug: "{{slug}}"
    fields:
      - {
          label: "Template Key",
          name: "templateKey",
          widget: "hidden",
          default: "course-post",
        }
      - { label: "Course Title", name: "title", widget: "string" }
      - { label: "Instructor", name: "instructor", widget: "string" }
      - { label: "Course Level", name: "level", widget: "select", options: ["Beginner", "Intermediate", "Advanced", "All Levels"] }
      - { label: "Duration", name: "duration", widget: "string" }
      - { label: "Price", name: "price", widget: "string" }
      - {
          label: "Course Image",
          name: "courseImage",
          widget: image,
          choose_url: false
        }
      - { label: "Course Type", name: "courseType", widget: "select", options: ["Acting", "Voice", "Movement", "Script Analysis", "Improvisation", "Stage Combat", "Other"] }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Course Details", name: "body", widget: "markdown" }
      - { label: "Available", name: "available", widget: "boolean", default: true }