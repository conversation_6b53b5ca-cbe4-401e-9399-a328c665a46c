const path = require(`path`)
const { createFilePath } = require(`gatsby-source-filesystem`)

exports.createPages = ({ graphql, actions }) => {
  const { createPage } = actions

  // const blogPost = path.resolve(`./src/templates/blog-post.js`)
  return graphql(
    `
    {
      allMarkdownRemark(limit: 1000, sort: {frontmatter: {date: DESC}}) {
        edges {
          node {
            id
            fields {
              slug
            }
            frontmatter {
              templateKey
              title
              date(formatString: "DD:MM:YYYY hh:mm")

            }
          }
        }
      }
    }
    `
  ).then(result => {
    if (result.errors) {
      throw result.errors
    }
    const posts = result.data.allMarkdownRemark.edges

    // Template For blog-post
    const blogPost = posts.filter(item => item.node.frontmatter.templateKey === 'blog-post')
    blogPost.forEach((post, index) => {
      const previous = index === blogPost.length - 1 ? null : blogPost[index + 1].node
      const next = index === 0 ? null : blogPost[index - 1].node

      createPage({
        path: post.node.fields.slug,
        component: path.resolve(`src/templates/blog-post.js`),
        context: {
          id: post.node.id,
          slug: post.node.fields.slug,
          previous,
          next,
        },
      })
    })

    // Template For interview-post
    const interviewPost = posts.filter(item => item.node.frontmatter.templateKey === 'interview-post')
    interviewPost.forEach((post) => {
      createPage({
        path: post.node.fields.slug,
        component: path.resolve(`src/templates/interview-post.js`),
        context: {
          id: post.node.id,
          slug: post.node.fields.slug,
        },
      })
    })

    // Template For production-post
    const productionPost = posts.filter(item => item.node.frontmatter.templateKey === 'production-post')
    productionPost.forEach((post) => {
      createPage({
        path: post.node.fields.slug,
        component: path.resolve(`src/templates/production-post.js`),
        context: {
          id: post.node.id,
          slug: post.node.fields.slug,
        },
      })
    })

    // Template For course-post
    const coursePost = posts.filter(item => item.node.frontmatter.templateKey === 'course-post')
    coursePost.forEach((post) => {
      createPage({
        path: post.node.fields.slug,
        component: path.resolve(`src/templates/course-post.js`),
        context: {
          id: post.node.id,
          slug: post.node.fields.slug,
        },
      })
    })
  
    return null
  })
}
exports.onCreateNode = ({ node, actions, getNode }) => {
  const { createNodeField } = actions
  if (node.internal.type === `MarkdownRemark`) {
    const value = createFilePath({ node, getNode })
    createNodeField({
      name: `slug`,
      node,
      value,
    })
  }
}